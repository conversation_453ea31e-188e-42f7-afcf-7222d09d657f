import dspy

class QueryGenerator(dspy.Signature):
    """Generate a query based on question to fetch relevant context"""
    question: str = dspy.InputField()
    query: str = dspy.OutputField()


def search_wikipedia(query: str) -> list[str]:
    """Query ColBERT endpoint, which is a knowledge source based on wikipedia data"""
    results = dspy.ColBERTv2(url="http://************:2017/wiki17_abstracts")(query, k=1)
    return [x["text"] for x in results]

class RAG(dspy.Module):
    def __init__(self):
        self.query_generator = dspy.Predict(QueryGenerator)
        self.answer_generator = dspy.ChainOfThought("question, context -> answer")

    def forward(self, question, **kwargs):
        query = self.query_generator(question=question).query
        context = search_wikipedia(query)[0]
        answer = self.answer_generator(question=question, context=context).answer
        return answer
    

lm = dspy.LM('ollama_chat/qwen3:14b', api_base='http://*************:11434', api_key='')
dspy.configure(lm=lm)
rag = RAG()
print(RAG(question="Is Lebron James the basketball GOAT?"))