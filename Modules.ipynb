{"cells": [{"cell_type": "code", "execution_count": null, "id": "b3e0b12d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025/07/03 14:49:12 WARNING dspy.adapters.json_adapter: Failed to use structured output format, falling back to JSON mode.\n"]}, {"ename": "RuntimeError", "evalue": "Both structured output format and JSON mode failed. Please choose a model that supports `response_format` argument. Original error: litellm.APIConnectionError: Ollama_chatException - [Errno 65] No route to host", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mConnectError\u001b[39m                              <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/httpx/_transports/default.py:101\u001b[39m, in \u001b[36mmap_httpcore_exceptions\u001b[39m\u001b[34m()\u001b[39m\n\u001b[32m    100\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m101\u001b[39m     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n\u001b[32m    102\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/httpx/_transports/default.py:250\u001b[39m, in \u001b[36mHTTPTransport.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    249\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m map_httpcore_exceptions():\n\u001b[32m--> \u001b[39m\u001b[32m250\u001b[39m     resp = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_pool\u001b[49m\u001b[43m.\u001b[49m\u001b[43mhandle_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mreq\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    252\u001b[39m \u001b[38;5;28;01<PERSON>ert\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(resp.stream, typing.Iterable)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/httpcore/_sync/connection_pool.py:256\u001b[39m, in \u001b[36mConnectionPool.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    255\u001b[39m     \u001b[38;5;28mself\u001b[39m._close_connections(closing)\n\u001b[32m--> \u001b[39m\u001b[32m256\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m exc \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m    258\u001b[39m \u001b[38;5;66;03m# Return the response. Note that in this case we still have to manage\u001b[39;00m\n\u001b[32m    259\u001b[39m \u001b[38;5;66;03m# the point at which the response is closed.\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/httpcore/_sync/connection_pool.py:236\u001b[39m, in \u001b[36mConnectionPool.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    234\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    235\u001b[39m     \u001b[38;5;66;03m# Send the request on the assigned connection.\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m236\u001b[39m     response = \u001b[43mconnection\u001b[49m\u001b[43m.\u001b[49m\u001b[43mhandle_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    237\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpool_request\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrequest\u001b[49m\n\u001b[32m    238\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    239\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m ConnectionNotAvailable:\n\u001b[32m    240\u001b[39m     \u001b[38;5;66;03m# In some cases a connection may initially be available to\u001b[39;00m\n\u001b[32m    241\u001b[39m     \u001b[38;5;66;03m# handle a request, but then become unavailable.\u001b[39;00m\n\u001b[32m    242\u001b[39m     \u001b[38;5;66;03m#\u001b[39;00m\n\u001b[32m    243\u001b[39m     \u001b[38;5;66;03m# In this case we clear the connection and try again.\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/httpcore/_sync/connection.py:101\u001b[39m, in \u001b[36mHTTPConnection.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    100\u001b[39m     \u001b[38;5;28mself\u001b[39m._connect_failed = \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m101\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m exc\n\u001b[32m    103\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._connection.handle_request(request)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/httpcore/_sync/connection.py:78\u001b[39m, in \u001b[36mHTTPConnection.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m     77\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._connection \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m78\u001b[39m     stream = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_connect\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     80\u001b[39m     ssl_object = stream.get_extra_info(\u001b[33m\"\u001b[39m\u001b[33mssl_object\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/httpcore/_sync/connection.py:124\u001b[39m, in \u001b[36mHTTPConnection._connect\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    123\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m Trace(\u001b[33m\"\u001b[39m\u001b[33mconnect_tcp\u001b[39m\u001b[33m\"\u001b[39m, logger, request, kwargs) \u001b[38;5;28;01mas\u001b[39;00m trace:\n\u001b[32m--> \u001b[39m\u001b[32m124\u001b[39m     stream = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_network_backend\u001b[49m\u001b[43m.\u001b[49m\u001b[43mconnect_tcp\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    125\u001b[39m     trace.return_value = stream\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/httpcore/_backends/sync.py:207\u001b[39m, in \u001b[36mSyncBackend.connect_tcp\u001b[39m\u001b[34m(self, host, port, timeout, local_address, socket_options)\u001b[39m\n\u001b[32m    202\u001b[39m exc_map: ExceptionMapping = {\n\u001b[32m    203\u001b[39m     socket.timeout: ConnectTimeout,\n\u001b[32m    204\u001b[39m     \u001b[38;5;167;01mOSError\u001b[39;00m: ConnectError,\n\u001b[32m    205\u001b[39m }\n\u001b[32m--> \u001b[39m\u001b[32m207\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m map_exceptions(exc_map):\n\u001b[32m    208\u001b[39m     sock = socket.create_connection(\n\u001b[32m    209\u001b[39m         address,\n\u001b[32m    210\u001b[39m         timeout,\n\u001b[32m    211\u001b[39m         source_address=source_address,\n\u001b[32m    212\u001b[39m     )\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/contextlib.py:162\u001b[39m, in \u001b[36m_GeneratorContextManager.__exit__\u001b[39m\u001b[34m(self, typ, value, traceback)\u001b[39m\n\u001b[32m    161\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m162\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mgen\u001b[49m\u001b[43m.\u001b[49m\u001b[43mthrow\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvalue\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    163\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mStopIteration\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[32m    164\u001b[39m     \u001b[38;5;66;03m# Suppress StopIteration *unless* it's the same exception that\u001b[39;00m\n\u001b[32m    165\u001b[39m     \u001b[38;5;66;03m# was passed to throw().  This prevents a StopIteration\u001b[39;00m\n\u001b[32m    166\u001b[39m     \u001b[38;5;66;03m# raised inside the \"with\" statement from being suppressed.\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/httpcore/_exceptions.py:14\u001b[39m, in \u001b[36mmap_exceptions\u001b[39m\u001b[34m(map)\u001b[39m\n\u001b[32m     13\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(exc, from_exc):\n\u001b[32m---> \u001b[39m\u001b[32m14\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m to_exc(exc) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mexc\u001b[39;00m\n\u001b[32m     15\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m\n", "\u001b[31mConnectError\u001b[39m: [Errno 65] No route to host", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31mConnectError\u001b[39m                              <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/litellm/llms/custom_httpx/llm_http_handler.py:171\u001b[39m, in \u001b[36mBaseLLMHTTPHandler._make_common_sync_call\u001b[39m\u001b[34m(self, sync_httpx_client, provider_config, api_base, headers, data, timeout, litellm_params, logging_obj, stream, signed_json_body)\u001b[39m\n\u001b[32m    170\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m171\u001b[39m     response = \u001b[43msync_httpx_client\u001b[49m\u001b[43m.\u001b[49m\u001b[43mpost\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    172\u001b[39m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[43m=\u001b[49m\u001b[43mapi_base\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    173\u001b[39m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m=\u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    174\u001b[39m \u001b[43m        \u001b[49m\u001b[43mdata\u001b[49m\u001b[43m=\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    175\u001b[39m \u001b[43m            \u001b[49m\u001b[43msigned_json_body\u001b[49m\n\u001b[32m    176\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43msigned_json_body\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mis\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mnot\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\n\u001b[32m    177\u001b[39m \u001b[43m            \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mjson\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdumps\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    178\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    179\u001b[39m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    180\u001b[39m \u001b[43m        \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    181\u001b[39m \u001b[43m        \u001b[49m\u001b[43mlogging_obj\u001b[49m\u001b[43m=\u001b[49m\u001b[43mlogging_obj\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    182\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    183\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m httpx.HTTPStatusError \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/litellm/llms/custom_httpx/http_handler.py:756\u001b[39m, in \u001b[36mHTTPHandler.post\u001b[39m\u001b[34m(self, url, data, json, params, headers, stream, timeout, files, content, logging_obj)\u001b[39m\n\u001b[32m    755\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m--> \u001b[39m\u001b[32m756\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m e\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/litellm/llms/custom_httpx/http_handler.py:735\u001b[39m, in \u001b[36mHTTPHandler.post\u001b[39m\u001b[34m(self, url, data, json, params, headers, stream, timeout, files, content, logging_obj)\u001b[39m\n\u001b[32m    732\u001b[39m     req = \u001b[38;5;28mself\u001b[39m.client.build_request(\n\u001b[32m    733\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mPOST\u001b[39m\u001b[33m\"\u001b[39m, url, data=data, json=json, params=params, headers=headers, files=files, content=content  \u001b[38;5;66;03m# type: ignore\u001b[39;00m\n\u001b[32m    734\u001b[39m     )\n\u001b[32m--> \u001b[39m\u001b[32m735\u001b[39m response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mclient\u001b[49m\u001b[43m.\u001b[49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mreq\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    736\u001b[39m response.raise_for_status()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/httpx/_client.py:914\u001b[39m, in \u001b[36mClient.send\u001b[39m\u001b[34m(self, request, stream, auth, follow_redirects)\u001b[39m\n\u001b[32m    912\u001b[39m auth = \u001b[38;5;28mself\u001b[39m._build_request_auth(request, auth)\n\u001b[32m--> \u001b[39m\u001b[32m914\u001b[39m response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_send_handling_auth\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    915\u001b[39m \u001b[43m    \u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    916\u001b[39m \u001b[43m    \u001b[49m\u001b[43mauth\u001b[49m\u001b[43m=\u001b[49m\u001b[43mauth\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    917\u001b[39m \u001b[43m    \u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m=\u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    918\u001b[39m \u001b[43m    \u001b[49m\u001b[43mhistory\u001b[49m\u001b[43m=\u001b[49m\u001b[43m[\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    919\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    920\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/httpx/_client.py:942\u001b[39m, in \u001b[36mClient._send_handling_auth\u001b[39m\u001b[34m(self, request, auth, follow_redirects, history)\u001b[39m\n\u001b[32m    941\u001b[39m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m942\u001b[39m     response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_send_handling_redirects\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    943\u001b[39m \u001b[43m        \u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    944\u001b[39m \u001b[43m        \u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m=\u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    945\u001b[39m \u001b[43m        \u001b[49m\u001b[43mhistory\u001b[49m\u001b[43m=\u001b[49m\u001b[43mhistory\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    946\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    947\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/httpx/_client.py:979\u001b[39m, in \u001b[36mClient._send_handling_redirects\u001b[39m\u001b[34m(self, request, follow_redirects, history)\u001b[39m\n\u001b[32m    977\u001b[39m     hook(request)\n\u001b[32m--> \u001b[39m\u001b[32m979\u001b[39m response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_send_single_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    980\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/httpx/_client.py:1014\u001b[39m, in \u001b[36mClient._send_single_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m   1013\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m request_context(request=request):\n\u001b[32m-> \u001b[39m\u001b[32m1014\u001b[39m     response = \u001b[43mtransport\u001b[49m\u001b[43m.\u001b[49m\u001b[43mhandle_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1016\u001b[39m \u001b[38;5;28;01<PERSON>ert\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(response.stream, SyncByteStream)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/httpx/_transports/default.py:249\u001b[39m, in \u001b[36mHTTPTransport.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    237\u001b[39m req = httpcore.Request(\n\u001b[32m    238\u001b[39m     method=request.method,\n\u001b[32m    239\u001b[39m     url=httpcore.URL(\n\u001b[32m   (...)\u001b[39m\u001b[32m    247\u001b[39m     extensions=request.extensions,\n\u001b[32m    248\u001b[39m )\n\u001b[32m--> \u001b[39m\u001b[32m249\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m map_httpcore_exceptions():\n\u001b[32m    250\u001b[39m     resp = \u001b[38;5;28mself\u001b[39m._pool.handle_request(req)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/contextlib.py:162\u001b[39m, in \u001b[36m_GeneratorContextManager.__exit__\u001b[39m\u001b[34m(self, typ, value, traceback)\u001b[39m\n\u001b[32m    161\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m162\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mgen\u001b[49m\u001b[43m.\u001b[49m\u001b[43mthrow\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvalue\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    163\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mStopIteration\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[32m    164\u001b[39m     \u001b[38;5;66;03m# Suppress StopIteration *unless* it's the same exception that\u001b[39;00m\n\u001b[32m    165\u001b[39m     \u001b[38;5;66;03m# was passed to throw().  This prevents a StopIteration\u001b[39;00m\n\u001b[32m    166\u001b[39m     \u001b[38;5;66;03m# raised inside the \"with\" statement from being suppressed.\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/httpx/_transports/default.py:118\u001b[39m, in \u001b[36mmap_httpcore_exceptions\u001b[39m\u001b[34m()\u001b[39m\n\u001b[32m    117\u001b[39m message = \u001b[38;5;28mstr\u001b[39m(exc)\n\u001b[32m--> \u001b[39m\u001b[32m118\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m mapped_exc(message) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mexc\u001b[39;00m\n", "\u001b[31mConnectError\u001b[39m: [Errno 65] No route to host", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                               <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/litellm/main.py:3031\u001b[39m, in \u001b[36mcompletion\u001b[39m\u001b[34m(model, messages, timeout, temperature, top_p, n, stream, stream_options, stop, max_completion_tokens, max_tokens, modalities, prediction, audio, presence_penalty, frequency_penalty, logit_bias, user, reasoning_effort, response_format, seed, tools, tool_choice, logprobs, top_logprobs, parallel_tool_calls, web_search_options, deployment_id, extra_headers, functions, function_call, base_url, api_version, api_key, model_list, thinking, **kwargs)\u001b[39m\n\u001b[32m   3024\u001b[39m     api_key = (\n\u001b[32m   3025\u001b[39m         api_key\n\u001b[32m   3026\u001b[39m         \u001b[38;5;129;01mor\u001b[39;00m litellm.ollama_key\n\u001b[32m   3027\u001b[39m         \u001b[38;5;129;01mor\u001b[39;00m os.environ.get(\u001b[33m\"\u001b[39m\u001b[33mOLLAMA_API_KEY\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m   3028\u001b[39m         \u001b[38;5;129;01mor\u001b[39;00m litellm.api_key\n\u001b[32m   3029\u001b[39m     )\n\u001b[32m-> \u001b[39m\u001b[32m3031\u001b[39m     response = \u001b[43mbase_llm_http_handler\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcompletion\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   3032\u001b[39m \u001b[43m        \u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3033\u001b[39m \u001b[43m        \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3034\u001b[39m \u001b[43m        \u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3035\u001b[39m \u001b[43m        \u001b[49m\u001b[43macompletion\u001b[49m\u001b[43m=\u001b[49m\u001b[43macompletion\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3036\u001b[39m \u001b[43m        \u001b[49m\u001b[43mapi_base\u001b[49m\u001b[43m=\u001b[49m\u001b[43mapi_base\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3037\u001b[39m \u001b[43m        \u001b[49m\u001b[43mmodel_response\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmodel_response\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3038\u001b[39m \u001b[43m        \u001b[49m\u001b[43moptional_params\u001b[49m\u001b[43m=\u001b[49m\u001b[43moptional_params\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3039\u001b[39m \u001b[43m        \u001b[49m\u001b[43mlitellm_params\u001b[49m\u001b[43m=\u001b[49m\u001b[43mlitellm_params\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3040\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcustom_llm_provider\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mollama_chat\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m   3041\u001b[39m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3042\u001b[39m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m=\u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3043\u001b[39m \u001b[43m        \u001b[49m\u001b[43mencoding\u001b[49m\u001b[43m=\u001b[49m\u001b[43mencoding\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3044\u001b[39m \u001b[43m        \u001b[49m\u001b[43mapi_key\u001b[49m\u001b[43m=\u001b[49m\u001b[43mapi_key\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3045\u001b[39m \u001b[43m        \u001b[49m\u001b[43mlogging_obj\u001b[49m\u001b[43m=\u001b[49m\u001b[43mlogging\u001b[49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# model call logging done inside the class as we make need to modify I/O to fit aleph alpha's requirements\u001b[39;49;00m\n\u001b[32m   3046\u001b[39m \u001b[43m        \u001b[49m\u001b[43mclient\u001b[49m\u001b[43m=\u001b[49m\u001b[43mclient\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3047\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   3049\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m custom_llm_provider == \u001b[33m\"\u001b[39m\u001b[33mtriton\u001b[39m\u001b[33m\"\u001b[39m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/litellm/llms/custom_httpx/llm_http_handler.py:466\u001b[39m, in \u001b[36mBaseLLMHTTPHandler.completion\u001b[39m\u001b[34m(self, model, messages, api_base, custom_llm_provider, model_response, encoding, logging_obj, optional_params, timeout, litellm_params, acompletion, stream, fake_stream, api_key, headers, client, provider_config)\u001b[39m\n\u001b[32m    464\u001b[39m     sync_httpx_client = client\n\u001b[32m--> \u001b[39m\u001b[32m466\u001b[39m response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_make_common_sync_call\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    467\u001b[39m \u001b[43m    \u001b[49m\u001b[43msync_httpx_client\u001b[49m\u001b[43m=\u001b[49m\u001b[43msync_httpx_client\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    468\u001b[39m \u001b[43m    \u001b[49m\u001b[43mprovider_config\u001b[49m\u001b[43m=\u001b[49m\u001b[43mprovider_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    469\u001b[39m \u001b[43m    \u001b[49m\u001b[43mapi_base\u001b[49m\u001b[43m=\u001b[49m\u001b[43mapi_base\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    470\u001b[39m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m=\u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    471\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdata\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    472\u001b[39m \u001b[43m    \u001b[49m\u001b[43msigned_json_body\u001b[49m\u001b[43m=\u001b[49m\u001b[43msigned_json_body\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    473\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    474\u001b[39m \u001b[43m    \u001b[49m\u001b[43mlitellm_params\u001b[49m\u001b[43m=\u001b[49m\u001b[43mlitellm_params\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    475\u001b[39m \u001b[43m    \u001b[49m\u001b[43mlogging_obj\u001b[49m\u001b[43m=\u001b[49m\u001b[43mlogging_obj\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    476\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    477\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m provider_config.transform_response(\n\u001b[32m    478\u001b[39m     model=model,\n\u001b[32m    479\u001b[39m     raw_response=response,\n\u001b[32m   (...)\u001b[39m\u001b[32m    488\u001b[39m     json_mode=json_mode,\n\u001b[32m    489\u001b[39m )\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/litellm/llms/custom_httpx/llm_http_handler.py:198\u001b[39m, in \u001b[36mBaseLLMHTTPHandler._make_common_sync_call\u001b[39m\u001b[34m(self, sync_httpx_client, provider_config, api_base, headers, data, timeout, litellm_params, logging_obj, stream, signed_json_body)\u001b[39m\n\u001b[32m    197\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m--> \u001b[39m\u001b[32m198\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_handle_error\u001b[49m\u001b[43m(\u001b[49m\u001b[43me\u001b[49m\u001b[43m=\u001b[49m\u001b[43me\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mprovider_config\u001b[49m\u001b[43m=\u001b[49m\u001b[43mprovider_config\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    199\u001b[39m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/litellm/llms/custom_httpx/llm_http_handler.py:2396\u001b[39m, in \u001b[36mBaseLLMHTTPHandler._handle_error\u001b[39m\u001b[34m(self, e, provider_config)\u001b[39m\n\u001b[32m   2390\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m BaseLLMException(\n\u001b[32m   2391\u001b[39m         status_code=status_code,\n\u001b[32m   2392\u001b[39m         message=error_text,\n\u001b[32m   2393\u001b[39m         headers=error_headers,\n\u001b[32m   2394\u001b[39m     )\n\u001b[32m-> \u001b[39m\u001b[32m2396\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m provider_config.get_error_class(\n\u001b[32m   2397\u001b[39m     error_message=error_text,\n\u001b[32m   2398\u001b[39m     status_code=status_code,\n\u001b[32m   2399\u001b[39m     headers=error_headers,\n\u001b[32m   2400\u001b[39m )\n", "\u001b[31mOllamaError\u001b[39m: [Errno 65] No route to host", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[31mAPIConnectionError\u001b[39m                        Traceback (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/litellm/utils.py:1179\u001b[39m, in \u001b[36mclient.<locals>.wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m   1178\u001b[39m \u001b[38;5;66;03m# MODEL CALL\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m1179\u001b[39m result = \u001b[43moriginal_function\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1180\u001b[39m end_time = datetime.datetime.now()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/litellm/main.py:3311\u001b[39m, in \u001b[36mcompletion\u001b[39m\u001b[34m(model, messages, timeout, temperature, top_p, n, stream, stream_options, stop, max_completion_tokens, max_tokens, modalities, prediction, audio, presence_penalty, frequency_penalty, logit_bias, user, reasoning_effort, response_format, seed, tools, tool_choice, logprobs, top_logprobs, parallel_tool_calls, web_search_options, deployment_id, extra_headers, functions, function_call, base_url, api_version, api_key, model_list, thinking, **kwargs)\u001b[39m\n\u001b[32m   3309\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m   3310\u001b[39m     \u001b[38;5;66;03m## Map to OpenAI Exception\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m3311\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[43mexception_type\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   3312\u001b[39m \u001b[43m        \u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3313\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcustom_llm_provider\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcustom_llm_provider\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3314\u001b[39m \u001b[43m        \u001b[49m\u001b[43moriginal_exception\u001b[49m\u001b[43m=\u001b[49m\u001b[43me\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3315\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcompletion_kwargs\u001b[49m\u001b[43m=\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3316\u001b[39m \u001b[43m        \u001b[49m\u001b[43mextra_kwargs\u001b[49m\u001b[43m=\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3317\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/litellm/litellm_core_utils/exception_mapping_utils.py:2284\u001b[39m, in \u001b[36mexception_type\u001b[39m\u001b[34m(model, original_exception, custom_llm_provider, completion_kwargs, extra_kwargs)\u001b[39m\n\u001b[32m   2283\u001b[39m     \u001b[38;5;28msetattr\u001b[39m(e, \u001b[33m\"\u001b[39m\u001b[33mlitellm_response_headers\u001b[39m\u001b[33m\"\u001b[39m, litellm_response_headers)\n\u001b[32m-> \u001b[39m\u001b[32m2284\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[32m   2285\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/litellm/litellm_core_utils/exception_mapping_utils.py:2253\u001b[39m, in \u001b[36mexception_type\u001b[39m\u001b[34m(model, original_exception, custom_llm_provider, completion_kwargs, extra_kwargs)\u001b[39m\n\u001b[32m   2252\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(original_exception, \u001b[33m\"\u001b[39m\u001b[33mrequest\u001b[39m\u001b[33m\"\u001b[39m):\n\u001b[32m-> \u001b[39m\u001b[32m2253\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m APIConnectionError(\n\u001b[32m   2254\u001b[39m         message=\u001b[33m\"\u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[33m - \u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[33m\"\u001b[39m.format(exception_provider, error_str),\n\u001b[32m   2255\u001b[39m         llm_provider=custom_llm_provider,\n\u001b[32m   2256\u001b[39m         model=model,\n\u001b[32m   2257\u001b[39m         request=original_exception.request,\n\u001b[32m   2258\u001b[39m     )\n\u001b[32m   2259\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n", "\u001b[31mAPIConnectionError\u001b[39m: litellm.APIConnectionError: Ollama_chatException - [Errno 65] No route to host", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[31mAPIConnectionError\u001b[39m                        Traceback (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/dspy/adapters/json_adapter.py:69\u001b[39m, in \u001b[36mJSONAdapter.__call__\u001b[39m\u001b[34m(self, lm, lm_kwargs, signature, demos, inputs)\u001b[39m\n\u001b[32m     68\u001b[39m     lm_kwargs[\u001b[33m\"\u001b[39m\u001b[33mresponse_format\u001b[39m\u001b[33m\"\u001b[39m] = {\u001b[33m\"\u001b[39m\u001b[33mtype\u001b[39m\u001b[33m\"\u001b[39m: \u001b[33m\"\u001b[39m\u001b[33mjson_object\u001b[39m\u001b[33m\"\u001b[39m}\n\u001b[32m---> \u001b[39m\u001b[32m69\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[34;43m__call__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mlm\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlm_kwargs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msignature\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdemos\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minputs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     70\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m AdapterParseError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m     71\u001b[39m     \u001b[38;5;66;03m# On AdapterParseError, we raise the original error.\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/dspy/adapters/chat_adapter.py:50\u001b[39m, in \u001b[36mChatAdapter.__call__\u001b[39m\u001b[34m(self, lm, lm_kwargs, signature, demos, inputs)\u001b[39m\n\u001b[32m     47\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(e, ContextWindowExceededError) \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(\u001b[38;5;28mself\u001b[39m, JSONAdapter):\n\u001b[32m     48\u001b[39m     \u001b[38;5;66;03m# On context window exceeded error or already using JSONAdapter, we don't want to retry with a different\u001b[39;00m\n\u001b[32m     49\u001b[39m     \u001b[38;5;66;03m# adapter.\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m50\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[32m     51\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m JSONAdapter()(lm, lm_kwargs, signature, demos, inputs)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/dspy/adapters/chat_adapter.py:42\u001b[39m, in \u001b[36mChatAdapter.__call__\u001b[39m\u001b[34m(self, lm, lm_kwargs, signature, demos, inputs)\u001b[39m\n\u001b[32m     41\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m42\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[34;43m__call__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mlm\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlm_kwargs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msignature\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdemos\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minputs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     43\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m     44\u001b[39m     \u001b[38;5;66;03m# fallback to J<PERSON><PERSON>dapter\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/dspy/adapters/base.py:119\u001b[39m, in \u001b[36mAdapter.__call__\u001b[39m\u001b[34m(self, lm, lm_kwargs, signature, demos, inputs)\u001b[39m\n\u001b[32m    117\u001b[39m inputs = \u001b[38;5;28mself\u001b[39m.format(processed_signature, demos, inputs)\n\u001b[32m--> \u001b[39m\u001b[32m119\u001b[39m outputs = \u001b[43mlm\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m=\u001b[49m\u001b[43minputs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mlm_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    120\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._call_postprocess(signature, outputs)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/dspy/utils/callback.py:326\u001b[39m, in \u001b[36mwith_callbacks.<locals>.sync_wrapper\u001b[39m\u001b[34m(instance, *args, **kwargs)\u001b[39m\n\u001b[32m    325\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m callbacks:\n\u001b[32m--> \u001b[39m\u001b[32m326\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfn\u001b[49m\u001b[43m(\u001b[49m\u001b[43minstance\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    328\u001b[39m call_id = uuid.uuid4().hex\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/dspy/clients/base_lm.py:96\u001b[39m, in \u001b[36mBaseLM.__call__\u001b[39m\u001b[34m(self, prompt, messages, **kwargs)\u001b[39m\n\u001b[32m     94\u001b[39m \u001b[38;5;129m@with_callbacks\u001b[39m\n\u001b[32m     95\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m__call__\u001b[39m(\u001b[38;5;28mself\u001b[39m, prompt=\u001b[38;5;28;01mNone\u001b[39;00m, messages=\u001b[38;5;28;01mNone\u001b[39;00m, **kwargs):\n\u001b[32m---> \u001b[39m\u001b[32m96\u001b[39m     response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mforward\u001b[49m\u001b[43m(\u001b[49m\u001b[43mprompt\u001b[49m\u001b[43m=\u001b[49m\u001b[43mprompt\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     97\u001b[39m     outputs = \u001b[38;5;28mself\u001b[39m._process_lm_response(response, prompt, messages, **kwargs)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/dspy/clients/lm.py:127\u001b[39m, in \u001b[36mLM.forward\u001b[39m\u001b[34m(self, prompt, messages, **kwargs)\u001b[39m\n\u001b[32m    125\u001b[39m completion, litellm_cache_args = \u001b[38;5;28mself\u001b[39m._get_cached_completion_fn(completion, cache, enable_memory_cache)\n\u001b[32m--> \u001b[39m\u001b[32m127\u001b[39m results = \u001b[43mcompletion\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    128\u001b[39m \u001b[43m    \u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mdict\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    129\u001b[39m \u001b[43m    \u001b[49m\u001b[43mnum_retries\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mnum_retries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    130\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcache\u001b[49m\u001b[43m=\u001b[49m\u001b[43mlitellm_cache_args\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    131\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    133\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28many\u001b[39m(c.finish_reason == \u001b[33m\"\u001b[39m\u001b[33mlength\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m c \u001b[38;5;129;01min\u001b[39;00m results[\u001b[33m\"\u001b[39m\u001b[33mchoices\u001b[39m\u001b[33m\"\u001b[39m]):\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/dspy/clients/cache.py:232\u001b[39m, in \u001b[36mrequest_cache.<locals>.decorator.<locals>.sync_wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m    231\u001b[39m \u001b[38;5;66;03m# Otherwise, compute and store the result\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m232\u001b[39m result = \u001b[43mfn\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    233\u001b[39m \u001b[38;5;66;03m# `enable_memory_cache` can be provided at call time to avoid indefinite growth.\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/dspy/clients/lm.py:304\u001b[39m, in \u001b[36mlitellm_completion\u001b[39m\u001b[34m(request, num_retries, cache)\u001b[39m\n\u001b[32m    303\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m stream_completion \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m304\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mlitellm\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcompletion\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    305\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcache\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcache\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    306\u001b[39m \u001b[43m        \u001b[49m\u001b[43mnum_retries\u001b[49m\u001b[43m=\u001b[49m\u001b[43mnum_retries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    307\u001b[39m \u001b[43m        \u001b[49m\u001b[43mretry_strategy\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mexponential_backoff_retry\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m    308\u001b[39m \u001b[43m        \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    309\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    311\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m stream_completion()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/litellm/utils.py:1284\u001b[39m, in \u001b[36mclient.<locals>.wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m   1283\u001b[39m         kwargs[\u001b[33m\"\u001b[39m\u001b[33mnum_retries\u001b[39m\u001b[33m\"\u001b[39m] = num_retries\n\u001b[32m-> \u001b[39m\u001b[32m1284\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mlitellm\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcompletion_with_retries\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1285\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m (\n\u001b[32m   1286\u001b[39m     \u001b[38;5;28misinstance\u001b[39m(e, litellm.exceptions.ContextWindowExceededError)\n\u001b[32m   1287\u001b[39m     \u001b[38;5;129;01mand\u001b[39;00m context_window_fallback_dict\n\u001b[32m   1288\u001b[39m     \u001b[38;5;129;01mand\u001b[39;00m model \u001b[38;5;129;01min\u001b[39;00m context_window_fallback_dict\n\u001b[32m   1289\u001b[39m     \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m _is_litellm_router_call\n\u001b[32m   1290\u001b[39m ):\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/litellm/main.py:3349\u001b[39m, in \u001b[36mcompletion_with_retries\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m   3346\u001b[39m     retryer = tenacity.Retrying(\n\u001b[32m   3347\u001b[39m         stop=tenacity.stop_after_attempt(num_retries), reraise=\u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[32m   3348\u001b[39m     )\n\u001b[32m-> \u001b[39m\u001b[32m3349\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mretryer\u001b[49m\u001b[43m(\u001b[49m\u001b[43moriginal_function\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/tenacity/__init__.py:475\u001b[39m, in \u001b[36mRetrying.__call__\u001b[39m\u001b[34m(self, fn, *args, **kwargs)\u001b[39m\n\u001b[32m    474\u001b[39m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m475\u001b[39m     do = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43miter\u001b[49m\u001b[43m(\u001b[49m\u001b[43mretry_state\u001b[49m\u001b[43m=\u001b[49m\u001b[43mretry_state\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    476\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(do, DoAttempt):\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/tenacity/__init__.py:376\u001b[39m, in \u001b[36mBaseRetrying.iter\u001b[39m\u001b[34m(self, retry_state)\u001b[39m\n\u001b[32m    375\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m action \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m.iter_state.actions:\n\u001b[32m--> \u001b[39m\u001b[32m376\u001b[39m     result = \u001b[43maction\u001b[49m\u001b[43m(\u001b[49m\u001b[43mretry_state\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    377\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m result\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/tenacity/__init__.py:418\u001b[39m, in \u001b[36mBaseRetrying._post_stop_check_actions.<locals>.exc_check\u001b[39m\u001b[34m(rs)\u001b[39m\n\u001b[32m    417\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.reraise:\n\u001b[32m--> \u001b[39m\u001b[32m418\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[43mretry_exc\u001b[49m\u001b[43m.\u001b[49m\u001b[43mreraise\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    419\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m retry_exc \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mfut\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mexception\u001b[39;00m()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/tenacity/__init__.py:185\u001b[39m, in \u001b[36mRetryError.reraise\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    184\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.last_attempt.failed:\n\u001b[32m--> \u001b[39m\u001b[32m185\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mlast_attempt\u001b[49m\u001b[43m.\u001b[49m\u001b[43mresult\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    186\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/concurrent/futures/_base.py:449\u001b[39m, in \u001b[36mFuture.result\u001b[39m\u001b[34m(self, timeout)\u001b[39m\n\u001b[32m    448\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._state == FINISHED:\n\u001b[32m--> \u001b[39m\u001b[32m449\u001b[39m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m__get_result\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    451\u001b[39m \u001b[38;5;28mself\u001b[39m._condition.wait(timeout)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/concurrent/futures/_base.py:401\u001b[39m, in \u001b[36mFuture.__get_result\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    400\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m401\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m._exception\n\u001b[32m    402\u001b[39m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[32m    403\u001b[39m     \u001b[38;5;66;03m# Break a reference cycle with the exception in self._exception\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/tenacity/__init__.py:478\u001b[39m, in \u001b[36mRetrying.__call__\u001b[39m\u001b[34m(self, fn, *args, **kwargs)\u001b[39m\n\u001b[32m    477\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m478\u001b[39m     result = \u001b[43mfn\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    479\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m:  \u001b[38;5;66;03m# noqa: B902\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/litellm/utils.py:1304\u001b[39m, in \u001b[36mclient.<locals>.wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m   1301\u001b[39m     logging_obj.failure_handler(\n\u001b[32m   1302\u001b[39m         e, traceback_exception, start_time, end_time\n\u001b[32m   1303\u001b[39m     )  \u001b[38;5;66;03m# DO NOT MAKE THREADED - router retry fallback relies on this!\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m1304\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m e\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/litellm/utils.py:1179\u001b[39m, in \u001b[36mclient.<locals>.wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m   1178\u001b[39m \u001b[38;5;66;03m# MODEL CALL\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m1179\u001b[39m result = \u001b[43moriginal_function\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1180\u001b[39m end_time = datetime.datetime.now()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/litellm/main.py:3311\u001b[39m, in \u001b[36mcompletion\u001b[39m\u001b[34m(model, messages, timeout, temperature, top_p, n, stream, stream_options, stop, max_completion_tokens, max_tokens, modalities, prediction, audio, presence_penalty, frequency_penalty, logit_bias, user, reasoning_effort, response_format, seed, tools, tool_choice, logprobs, top_logprobs, parallel_tool_calls, web_search_options, deployment_id, extra_headers, functions, function_call, base_url, api_version, api_key, model_list, thinking, **kwargs)\u001b[39m\n\u001b[32m   3309\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m   3310\u001b[39m     \u001b[38;5;66;03m## Map to OpenAI Exception\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m3311\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[43mexception_type\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   3312\u001b[39m \u001b[43m        \u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3313\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcustom_llm_provider\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcustom_llm_provider\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3314\u001b[39m \u001b[43m        \u001b[49m\u001b[43moriginal_exception\u001b[49m\u001b[43m=\u001b[49m\u001b[43me\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3315\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcompletion_kwargs\u001b[49m\u001b[43m=\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3316\u001b[39m \u001b[43m        \u001b[49m\u001b[43mextra_kwargs\u001b[49m\u001b[43m=\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   3317\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/litellm/litellm_core_utils/exception_mapping_utils.py:2284\u001b[39m, in \u001b[36mexception_type\u001b[39m\u001b[34m(model, original_exception, custom_llm_provider, completion_kwargs, extra_kwargs)\u001b[39m\n\u001b[32m   2283\u001b[39m     \u001b[38;5;28msetattr\u001b[39m(e, \u001b[33m\"\u001b[39m\u001b[33mlitellm_response_headers\u001b[39m\u001b[33m\"\u001b[39m, litellm_response_headers)\n\u001b[32m-> \u001b[39m\u001b[32m2284\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[32m   2285\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/litellm/litellm_core_utils/exception_mapping_utils.py:2253\u001b[39m, in \u001b[36mexception_type\u001b[39m\u001b[34m(model, original_exception, custom_llm_provider, completion_kwargs, extra_kwargs)\u001b[39m\n\u001b[32m   2252\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(original_exception, \u001b[33m\"\u001b[39m\u001b[33mrequest\u001b[39m\u001b[33m\"\u001b[39m):\n\u001b[32m-> \u001b[39m\u001b[32m2253\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m APIConnectionError(\n\u001b[32m   2254\u001b[39m         message=\u001b[33m\"\u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[33m - \u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[33m\"\u001b[39m.format(exception_provider, error_str),\n\u001b[32m   2255\u001b[39m         llm_provider=custom_llm_provider,\n\u001b[32m   2256\u001b[39m         model=model,\n\u001b[32m   2257\u001b[39m         request=original_exception.request,\n\u001b[32m   2258\u001b[39m     )\n\u001b[32m   2259\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n", "\u001b[31mAPIConnectionError\u001b[39m: litellm.APIConnectionError: Ollama_chatException - [Errno 65] No route to host", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31m<PERSON>unt<PERSON>E<PERSON><PERSON>\u001b[39m                              <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 6\u001b[39m\n\u001b[32m      3\u001b[39m dspy.configure(lm=lm)\n\u001b[32m      5\u001b[39m math = dspy.ChainOfThought(\u001b[33m\"\u001b[39m\u001b[33mquestion -> answer: float\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m----> \u001b[39m\u001b[32m6\u001b[39m \u001b[43mmath\u001b[49m\u001b[43m(\u001b[49m\u001b[43mquestion\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mTwo dice are tossed. What is the probability that the sum equals two?\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/dspy/utils/callback.py:326\u001b[39m, in \u001b[36mwith_callbacks.<locals>.sync_wrapper\u001b[39m\u001b[34m(instance, *args, **kwargs)\u001b[39m\n\u001b[32m    324\u001b[39m callbacks = _get_active_callbacks(instance)\n\u001b[32m    325\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m callbacks:\n\u001b[32m--> \u001b[39m\u001b[32m326\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfn\u001b[49m\u001b[43m(\u001b[49m\u001b[43minstance\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    328\u001b[39m call_id = uuid.uuid4().hex\n\u001b[32m    330\u001b[39m _execute_start_callbacks(instance, fn, call_id, callbacks, args, kwargs)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/dspy/primitives/program.py:60\u001b[39m, in \u001b[36mModule.__call__\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m     57\u001b[39m     output.set_lm_usage(usage_tracker.get_total_tokens())\n\u001b[32m     58\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m output\n\u001b[32m---> \u001b[39m\u001b[32m60\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mforward\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/dspy/predict/chain_of_thought.py:38\u001b[39m, in \u001b[36mChainOfThought.forward\u001b[39m\u001b[34m(self, **kwargs)\u001b[39m\n\u001b[32m     37\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mforward\u001b[39m(\u001b[38;5;28mself\u001b[39m, **kwargs):\n\u001b[32m---> \u001b[39m\u001b[32m38\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mpredict\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/dspy/predict/predict.py:85\u001b[39m, in \u001b[36mPredict.__call__\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m     82\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m args:\n\u001b[32m     83\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mV<PERSON><PERSON>Error\u001b[39;00m(\u001b[38;5;28mself\u001b[39m._get_positional_args_error_message())\n\u001b[32m---> \u001b[39m\u001b[32m85\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43ms<PERSON>r\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[34;43m__call__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/dspy/utils/callback.py:326\u001b[39m, in \u001b[36mwith_callbacks.<locals>.sync_wrapper\u001b[39m\u001b[34m(instance, *args, **kwargs)\u001b[39m\n\u001b[32m    324\u001b[39m callbacks = _get_active_callbacks(instance)\n\u001b[32m    325\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m callbacks:\n\u001b[32m--> \u001b[39m\u001b[32m326\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfn\u001b[49m\u001b[43m(\u001b[49m\u001b[43minstance\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    328\u001b[39m call_id = uuid.uuid4().hex\n\u001b[32m    330\u001b[39m _execute_start_callbacks(instance, fn, call_id, callbacks, args, kwargs)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/dspy/primitives/program.py:60\u001b[39m, in \u001b[36mModule.__call__\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m     57\u001b[39m     output.set_lm_usage(usage_tracker.get_total_tokens())\n\u001b[32m     58\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m output\n\u001b[32m---> \u001b[39m\u001b[32m60\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mforward\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/dspy/predict/predict.py:157\u001b[39m, in \u001b[36mPredict.forward\u001b[39m\u001b[34m(self, **kwargs)\u001b[39m\n\u001b[32m    155\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    156\u001b[39m     \u001b[38;5;28;01mwith\u001b[39;00m settings.context(send_stream=\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[32m--> \u001b[39m\u001b[32m157\u001b[39m         completions = \u001b[43madapter\u001b[49m\u001b[43m(\u001b[49m\u001b[43mlm\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlm_kwargs\u001b[49m\u001b[43m=\u001b[49m\u001b[43mconfig\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msignature\u001b[49m\u001b[43m=\u001b[49m\u001b[43msignature\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdemos\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdemos\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minputs\u001b[49m\u001b[43m=\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    159\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._forward_postprocess(completions, signature, **kwargs)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/dspy/adapters/chat_adapter.py:51\u001b[39m, in \u001b[36mChatAdapter.__call__\u001b[39m\u001b[34m(self, lm, lm_kwargs, signature, demos, inputs)\u001b[39m\n\u001b[32m     47\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(e, ContextWindowExceededError) \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(\u001b[38;5;28mself\u001b[39m, JSONAdapter):\n\u001b[32m     48\u001b[39m     \u001b[38;5;66;03m# On context window exceeded error or already using JSONAdapter, we don't want to retry with a different\u001b[39;00m\n\u001b[32m     49\u001b[39m     \u001b[38;5;66;03m# adapter.\u001b[39;00m\n\u001b[32m     50\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[32m---> \u001b[39m\u001b[32m51\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mJSONAdapter\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m(\u001b[49m\u001b[43mlm\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlm_kwargs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msignature\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdemos\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minputs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.pyenv/versions/3.13.5/lib/python3.13/site-packages/dspy/adapters/json_adapter.py:75\u001b[39m, in \u001b[36mJSONAdapter.__call__\u001b[39m\u001b[34m(self, lm, lm_kwargs, signature, demos, inputs)\u001b[39m\n\u001b[32m     72\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[32m     73\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m     74\u001b[39m     \u001b[38;5;66;03m# On any other error, we raise a RuntimeError with the original error message.\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m75\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\n\u001b[32m     76\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mBoth structured output format and JSON mode failed. Please choose a model that supports \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m     77\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m`response_format` argument. Original error: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00me\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m\n\u001b[32m     78\u001b[39m     ) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01me\u001b[39;00m\n", "\u001b[31mRuntimeError\u001b[39m: Both structured output format and JSON mode failed. Please choose a model that supports `response_format` argument. Original error: litellm.APIConnectionError: Ollama_chatException - [Errno 65] No route to host"]}], "source": ["import dspy\n", "lm = dspy.LM(\"ollama_chat/qwen3:14b\", api_base=\"http://*************:11434\", api_key=\"\")\n", "dspy.configure(lm=lm)\n", "\n", "lm(\"Say this is a test!\", temperature=0.7)  # => ['This is a test!']\n", "math = dspy.ChainOfThought(\"question -> answer: float\")\n", "math(question=\"Two dice are tossed. What is the probability that the sum equals two?\")"]}, {"cell_type": "code", "execution_count": null, "id": "7064a785", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}