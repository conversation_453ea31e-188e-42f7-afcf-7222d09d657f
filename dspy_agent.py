from pydantic import BaseModel
import random
import string
import dspy
import os

class Date(BaseModel):
    # Somehow LLM is bad at specifying `datetime.datetime`, so
    # we define a custom class to represent the date.
    year: int
    month: int
    day: int
    hour: int

class UserProfile(BaseModel):
    user_id: str
    name: str
    email: str

class Flight(BaseModel):
    flight_id: str
    date_time: Date
    origin: str
    destination: str
    duration: float
    price: float

class Itinerary(BaseModel):
    confirmation_number: str
    user_profile: UserProfile
    flight: Flight

class Ticket(BaseModel):
    user_request: str
    user_profile: UserProfile


user_database = {
    "Adam": UserProfile(user_id="1", name="<PERSON>", email="<EMAIL>"),
    "Bob": UserProfile(user_id="2", name="<PERSON>", email="<EMAIL>"),
    "Chelsie": UserProfile(user_id="3", name="<PERSON><PERSON><PERSON>", email="<EMAIL>"),
    "<PERSON>": User<PERSON>rofile(user_id="4", name="<PERSON>", email="<EMAIL>"),
}

flight_database = {
    "DA123": Flight(
        flight_id="DA123",  # DSPy Airline 123
        origin="SFO",
        destination="JFK",
        date_time=Date(year=2025, month=9, day=1, hour=1),
        duration=3,
        price=200,
    ),
    "DA125": Flight(
        flight_id="DA125",
        origin="SFO",
        destination="JFK",
        date_time=Date(year=2025, month=9, day=1, hour=7),
        duration=9,
        price=500,
    ),
    "DA456": Flight(
        flight_id="DA456",
        origin="SFO",
        destination="SNA",
        date_time=Date(year=2025, month=10, day=1, hour=1),
        duration=2,
        price=100,
    ),
    "DA460": Flight(
        flight_id="DA460",
        origin="SFO",
        destination="SNA",
        date_time=Date(year=2025, month=10, day=1, hour=9),
        duration=2,
        price=120,
    ),
}

itinery_database = {}
ticket_database = {}


def fetch_flight_info(date: Date, origin: str, destination: str):
    """Fetch flight information from origin to destination on the given date"""

    flights = []
    for flight in flight_database.values():
        if (
            flight.origin == origin
            and flight.destination == destination
            and flight.date_time.year == date.year
            and flight.date_time.month == date.month
            and flight.date_time.day == date.day
        ):
            flights.append(flight)
    if len(flights) == 0:
        raise ValueError(f"No flights found from {origin} to {destination} on {date}")
    return flights


def fetch_itinery(confirmation_number: str):
    """Fetch itinerary information from confirmation number"""
    return itinery_database[confirmation_number]


def pick_flight(flights: list[Flight]):
    """Pick up the best flight that matches users' request. We pick the shortest and cheaper"""

    sorted_flights = sorted(
        flights,
        key = lambda x: (
            x["duration"] if isinstance(x, dict) else x.duration,
            x["price"] if isinstance(x, dict) else x.price,
        )
    )
    return sorted_flights[0]


def _generate_id(length=8):
    chars = string.ascii_lowercase + string.digits
    return "".join(random.choices(chars, k=length))


def book_flight(flight: Flight, user_profile: UserProfile):
    """Book a flight for the user"""

    confirmation_number = _generate_id()
    while confirmation_number in itinery_database:
        confirmation_number = _generate_id()
    itinery_database[confirmation_number] = Itınerary(
        confirmation_number=confirmation_number,
        user_profile=user_profile,
        flight=flight,
    )
    return confirmation_number, itinery_database[confirmation_number]


def cancel_itinerary(confirmation_number: str, user_profile: UserProfile):
    """Cancel an itinerary on behalf of the user"""

    if confirmation_number in itinery_database:
        del itinery_database[confirmation_number]
        return
    raise ValueError(f"Confirmation number {confirmation_number} not found")

def get_user_info(name: str):
    """Fetch the user profile from database with given name"""
    return user_database[name]

def file_ticket(user_request: str, user_profile: UserProfile):
    """File a customer support ticket if this is something the agent cannot handle"""
    ticket_id = _generate_id(length=6)
    ticket_database[ticket_id] = Ticket(
        user_request=user_request,
        user_profile=user_profile,
    )
    return ticket_id


import dspy

class DSPyAirlineCustomerService(dspy.Signature):
    """You are an airline customer service agent that helps user book and manage flights.
    
    You are given a list of tools to handle user request, and you should decide the right
    tool to use in order to fulfil users' request.
    """

    user_request: str = dspy.InputField()
    process_result: str = dspy.OutputField(
        desc=("Message that summerizes the process result, and the information users need,"
        "e.g the confirmation_number if a new flight is booked.")
    )

agent = dspy.ReAct(
    DSPyAirlineCustomerService,
    tools=[
        fetch_flight_info,
        fetch_itinery,
        pick_flight,
        book_flight,
        cancel_itinerary,
        get_user_info,
        file_ticket,
    ]
)

lm = dspy.LM('ollama_chat/qwen3:14b', api_base='http://192.168.0.105:11434', api_key='')
dspy.configure(lm=lm)

result = agent(user_request="please help me book a flight from SFO to JFK on 2025-09-01. My name is Adam.")
print(result)